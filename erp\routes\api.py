"""
API routes for database operations only
"""
from fastapi import APIRouter

# Only database-related API routes are needed
# All model CRUD operations have been removed as they were not required
# Database operations are handled in database.py routes

router = APIRouter(prefix="/api", tags=["api"])

# This router is now minimal and only includes database-related functionality
# which is handled by the database router in database.py
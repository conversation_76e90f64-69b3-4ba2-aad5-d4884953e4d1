"""
System information and health check routes
"""
from fastapi import APIRouter, Request
from fastapi.responses import HTMLResponse, RedirectResponse

from ..config import config
from ..database.registry import DatabaseRegistry
from ..utils.responses import APIResponse

router = APIRouter(tags=["system"])


@router.get("/")
async def root():
    """Root endpoint"""
    # If list_db is enabled, redirect to database list regardless of mode
    if config.list_db:
        return RedirectResponse(url="/app/databases", status_code=302)
    return APIResponse.success({"message": "ERP System API", "version": "1.0.0"})


@router.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Check database connection
        db = await DatabaseRegistry.get_current_database()
        if db:
            await db.execute("SELECT 1")
            db_status = "connected"
        else:
            db_status = "no_connection"
        
        return APIResponse.success({
            "status": "healthy",
            "database": db_status,
            "addons": "loaded"  # Will be updated when addon_loader is available
        })
    except Exception as e:
        return APIResponse.error(f"Health check failed: {str(e)}", status_code=503)


# Removed /addons and /models routes as they are not needed
# Only database-related routes and health check are kept
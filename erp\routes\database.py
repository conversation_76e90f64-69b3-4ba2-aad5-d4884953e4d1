"""
Database list viewing routes - only list viewing and redirection
"""
from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import HTMLResponse
from typing import Dict, Any, List
import re

from ..config import config
from ..database.registry import DatabaseRegistry
from ..templates.manager import get_template_manager

# Only view router for database listing - no API management routes
view_router = APIRouter(prefix="/databases", tags=["database-views"])


async def get_database_list() -> List[Dict[str, Any]]:
    """Helper function to get database list with information"""
    db_info_list = []

    if config.is_multi_db_mode:
        # Multi-database mode: list all databases with filter applied
        databases = await DatabaseRegistry.list_databases()

        for db_name in databases:
            # Apply database filter if configured
            if config.db_filter:
                if not re.match(config.db_filter, db_name):
                    continue

            # Get database size and other info
            try:
                db_manager = await DatabaseRegistry.get_database('postgres')
                size_query = f"SELECT pg_size_pretty(pg_database_size('{db_name}')) as size"
                size_result = await db_manager.fetch(size_query)
                size = size_result[0]['size'] if size_result else 'Unknown'

                # Get creation date (approximate)
                created_query = f"""
                    SELECT (pg_stat_file('base/'||oid||'/PG_VERSION')).modification as created
                    FROM pg_database WHERE datname = '{db_name}'
                """
                created_result = await db_manager.fetch(created_query)
                created = created_result[0]['created'].isoformat() if created_result else None

            except Exception as e:
                print(f"Error getting info for database {db_name}: {e}")
                size = 'Unknown'
                created = None

            db_info_list.append({
                'name': db_name,
                'size': size,
                'created': created,
                'owner': 'erp',  # Default owner
                'encoding': 'UTF8',  # Default encoding
                'status': 'active'  # Default status
            })
    else:
        # Single database mode: show only the configured database
        db_name = config.get_default_database()
        if db_name:
            # Apply filter even in single database mode
            if config.db_filter:
                if re.match(config.db_filter, db_name):
                    try:
                        db_manager = await DatabaseRegistry.get_database('postgres')
                        size_query = f"SELECT pg_size_pretty(pg_database_size('{db_name}')) as size"
                        size_result = await db_manager.fetch(size_query)
                        size = size_result[0]['size'] if size_result else 'Unknown'
                    except Exception:
                        size = 'Unknown'

                    db_info_list.append({
                        'name': db_name,
                        'size': size,
                        'created': None,
                        'owner': 'erp',
                        'encoding': 'UTF8',
                        'status': 'active'
                    })
            else:
                # No filter, show the single database
                try:
                    db_manager = await DatabaseRegistry.get_database('postgres')
                    size_query = f"SELECT pg_size_pretty(pg_database_size('{db_name}')) as size"
                    size_result = await db_manager.fetch(size_query)
                    size = size_result[0]['size'] if size_result else 'Unknown'
                except Exception:
                    size = 'Unknown'

                db_info_list.append({
                    'name': db_name,
                    'size': size,
                    'created': None,
                    'owner': 'erp',
                    'encoding': 'UTF8',
                    'status': 'active'
                })

    return db_info_list


# Template-based view routes
@view_router.get("", response_class=HTMLResponse)
async def database_list_page(request: Request):
    """Database list viewing page with direct template compilation"""
    if not config.list_db:
        raise HTTPException(status_code=403, detail="Database listing is disabled")

    template_manager = get_template_manager()

    try:
        # Load database list template if not already loaded
        try:
            await template_manager.load_template_file_async("database_simple.xml")
        except Exception:
            pass  # Template might already be loaded

        # Get database list directly and provide to template
        databases = await get_database_list()

        # Create config object for template
        class ConfigData:
            def __init__(self):
                self.is_multi_db_mode = config.is_multi_db_mode
                self.list_db = config.list_db
                self.db_filter = config.db_filter

        context = {
            'title': 'Database Selection',
            'databases': databases,
            'config': ConfigData(),
            'request': request
        }

        html_content = await template_manager.render_template_async('database_simple.html', context)
        return HTMLResponse(content=html_content)

    except Exception as e:
        # Use template for error display instead of inline HTML
        try:
            error_context = {
                'title': 'Database List Error',
                'error_message': str(e),
                'request': request
            }
            error_html = await template_manager.render_template_async('error.html', error_context)
            return HTMLResponse(content=error_html, status_code=500)
        except:
            # Fallback only if template system completely fails
            raise HTTPException(status_code=500, detail=f"Database listing failed: {str(e)}")


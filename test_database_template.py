#!/usr/bin/env python3
"""
Test script for the database management template
"""
import asyncio
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from erp.templates.manager import AsyncTemplateManager


async def test_database_template():
    """Test the database management template"""
    print("Testing Database Management Template...")
    
    # Create template manager
    manager = AsyncTemplateManager()
    
    # Clear any existing cache
    manager.clear_cache()
    
    # Add templates directory
    manager.add_template_directory("templates")
    
    try:
        # Load database management template
        print("\n1. Loading database management template...")
        await manager.load_template_file_async("database_simple.xml")
        print("✓ Database management template loaded")
        
        # Create mock config object
        class MockConfig:
            def __init__(self):
                self.is_multi_db_mode = True
                self.list_db = True
                self.db_filter = None
        
        # Create sample database data
        sample_databases = [
            {
                'name': 'production_db',
                'size': '125 MB',
                'created': '2024-01-15T10:30:00',
                'owner': 'erp',
                'encoding': 'UTF8',
                'status': 'active'
            },
            {
                'name': 'test_db',
                'size': '45 MB',
                'created': '2024-01-20T14:15:00',
                'owner': 'erp',
                'encoding': 'UTF8',
                'status': 'active'
            },
            {
                'name': 'demo_db',
                'size': '78 MB',
                'created': '2024-01-25T09:45:00',
                'owner': 'erp',
                'encoding': 'UTF8',
                'status': 'active'
            }
        ]
        
        context = {
            'title': 'Database Management',
            'databases': sample_databases,
            'config': MockConfig()
        }
        
        # Render template
        print("\n2. Rendering database management template...")
        result = await manager.render_template_async('database_simple.html', context)
        print(f"✓ Database management template rendered ({len(result)} characters)")
        
        # Save output for inspection
        with open("output_database_management.html", "w", encoding="utf-8") as f:
            f.write(result)
        print("  → Saved to output_database_management.html")
        
        # Test with empty database list
        print("\n3. Testing with empty database list...")
        empty_context = {
            'title': 'Database Management',
            'databases': [],
            'config': MockConfig()
        }
        
        empty_result = await manager.render_template_async('database_simple.html', empty_context)
        print(f"✓ Empty database list template rendered ({len(empty_result)} characters)")
        
        # Save empty output
        with open("output_database_management_empty.html", "w", encoding="utf-8") as f:
            f.write(empty_result)
        print("  → Saved to output_database_management_empty.html")
        
        # Test with single database mode
        print("\n4. Testing single database mode...")
        class SingleDbConfig:
            def __init__(self):
                self.is_multi_db_mode = False
                self.list_db = True
                self.db_filter = None
        
        single_context = {
            'title': 'Database Management',
            'databases': [sample_databases[0]],  # Only one database
            'config': SingleDbConfig()
        }
        
        single_result = await manager.render_template_async('database_simple.html', single_context)
        print(f"✓ Single database mode template rendered ({len(single_result)} characters)")
        
        # Save single mode output
        with open("output_database_management_single.html", "w", encoding="utf-8") as f:
            f.write(single_result)
        print("  → Saved to output_database_management_single.html")
        
    except Exception as e:
        print(f"✗ Database management template failed: {e}")
        import traceback
        traceback.print_exc()
    
    # List loaded templates
    print("\n5. Loaded templates:")
    templates = manager.list_templates()
    for template in templates:
        print(f"  - {template}")
    
    print(f"\nTotal templates loaded: {len(templates)}")
    print("\nDatabase template test completed!")


if __name__ == "__main__":
    asyncio.run(test_database_template())

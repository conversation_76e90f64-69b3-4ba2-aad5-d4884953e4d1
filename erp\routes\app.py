"""
Application and web interface routes
"""
from fastapi import APIRouter, Request, HTTPException
from fastapi.responses import HTMLResponse, RedirectResponse
import re

from ..config import config
from ..database.registry import DatabaseRegistry
from ..utils.responses import APIResponse
from ..templates.manager import get_template_manager

router = APIRouter(prefix="/app", tags=["app"])


@router.get("/databases", response_class=HTMLResponse)
async def database_list_page(request: Request):
    """Enhanced database selection page"""
    if not config.list_db:
        raise HTTPException(status_code=403, detail="Database listing is disabled")

    template_manager = get_template_manager()

    try:
        # Load enhanced database list template
        try:
            await template_manager.load_template_file_async("database_list_enhanced.xml")
        except Exception:
            pass  # Template might already be loaded

        # Create mock config object for template
        class MockConfig:
            def __init__(self):
                self.is_multi_db_mode = config.is_multi_db_mode
                self.list_db = config.list_db
                self.db_filter = config.db_filter

        context = {
            'title': 'Database Selection',
            'config': MockConfig(),
            'request': request
        }

        html_content = await template_manager.render_template_async('database_list_enhanced.html', context)
        return HTMLResponse(content=html_content)

    except Exception as e:
        print(f"Enhanced template rendering failed: {e}")
        # Fallback to simple HTML
        return HTMLResponse(content=f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Database Selection</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                .container {{ max-width: 600px; margin: 0 auto; }}
                h1 {{ color: #333; }}
                .error {{ color: #d32f2f; padding: 20px; background: #ffebee; border-radius: 4px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>Database Selection</h1>
                <div class="error">
                    <p>Template rendering failed: {str(e)}</p>
                    <p>Please check the template configuration.</p>
                </div>
            </div>
        </body>
        </html>
        """, status_code=500)

# Removed alternative route - only /app/databases is needed


@router.get("")
async def app_home(request: Request, db: str = None):
    """Main application home page with cookie support"""
    if db:
        # Database specified via query parameter, validate and set context
        if config.is_multi_db_mode:
            # In multi-database mode, validate against filter
            if config.db_filter:
                if not re.match(config.db_filter, db):
                    raise HTTPException(status_code=400, detail="Database not accessible")

            # Set database context for this request (middleware will handle cookie setting)
            DatabaseRegistry.set_current_database(db)
            return APIResponse.success({
                "message": f"Connected to database: {db}",
                "database": db,
                "mode": "multi-database",
                "cookie_set": True
            })
        else:
            # In single database mode, ignore the db parameter
            default_db = config.get_default_database()
            return APIResponse.success({
                "message": f"Using configured database: {default_db}",
                "database": default_db,
                "mode": "single-database"
            })
    else:
        # No database specified in query parameter
        if config.is_multi_db_mode:
            # Check if database is available from cookie (via middleware)
            db_name = getattr(request.state, 'db_name', None)
            if db_name:
                # Database found from cookie, use it
                return APIResponse.success({
                    "message": f"Connected to database from cookie: {db_name}",
                    "database": db_name,
                    "mode": "multi-database",
                    "source": "cookie"
                })
            else:
                # No database in cookie, redirect to database list
                return RedirectResponse(url="/app/databases", status_code=302)
        else:
            # Use default database
            default_db = config.get_default_database()
            return APIResponse.success({
                "message": f"Using default database: {default_db}",
                "database": default_db,
                "mode": "single-database"
            })


@router.get("/demo", response_class=HTMLResponse)
async def template_demo(request: Request):
    """Template engine demonstration page"""
    template_manager = get_template_manager()

    try:
        # Load demo template if not already loaded
        try:
            template_manager.load_template_file("demo_template.xml")
        except Exception:
            pass  # Template might already be loaded

        context = {
            'title': 'Template Engine Demo',
            'request': request
        }
        html_content = await template_manager.render_template_async('demo.template', context)
        return HTMLResponse(content=html_content)

    except Exception as e:
        return HTMLResponse(content=f"<h1>Template Demo Error</h1><p>Error: {str(e)}</p>", status_code=500)


@router.get("/home", response_class=HTMLResponse)
async def app_home_page(request: Request, db: str = None):
    """Application home page with template"""
    template_manager = get_template_manager()

    try:
        # Load app home template if not already loaded
        try:
            template_manager.load_template_file("app_home.xml")
        except Exception:
            pass  # Template might already be loaded

        # Get database info
        database_name = db or config.get_default_database()
        mode = "multi-database" if config.is_multi_db_mode else "single-database"

        context = {
            'title': 'ERP System',
            'subtitle': 'Welcome to your ERP dashboard',
            'database_name': database_name,
            'mode': mode,
            'database_connected': True,  # Simplified for demo
            'addon_count': 0,  # Will be updated when addon system is integrated
            'server_status': 'Running',
            'show_debug_info': False,
            'request': request
        }

        html_content = await template_manager.render_template_async('app_home.html', context)
        return HTMLResponse(content=html_content)

    except Exception as e:
        return HTMLResponse(content=f"<h1>App Home Error</h1><p>Error: {str(e)}</p>", status_code=500)
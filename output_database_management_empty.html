<html>
            <head>
                <title>Database Management</title><meta charset="utf-8"></meta>
                <meta name="viewport" content="width=device-width, initial-scale=1"></meta>
                <style>
                    body {
                        font-family: &#x27;Segoe UI&#x27;, Tahoma, Geneva, Verdana, sans-serif;
                        margin: 0;
                        padding: 20px;
                        background-color: #f5f5f5;
                    }
                    .container {
                        max-width: 1200px;
                        margin: 0 auto;
                        background: white;
                        padding: 30px;
                        border-radius: 8px;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    }
                    .header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 30px;
                        padding-bottom: 20px;
                        border-bottom: 2px solid #eee;
                    }
                    .btn {
                        padding: 10px 20px;
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                        text-decoration: none;
                        display: inline-block;
                        font-size: 14px;
                        transition: background-color 0.2s;
                    }
                    .btn-primary {
                        background-color: #007bff;
                        color: white;
                    }
                    .btn-primary:hover {
                        background-color: #0056b3;
                    }
                    .btn-danger {
                        background-color: #dc3545;
                        color: white;
                    }
                    .btn-danger:hover {
                        background-color: #c82333;
                    }
                    .btn-secondary {
                        background-color: #6c757d;
                        color: white;
                    }
                    .btn-secondary:hover {
                        background-color: #545b62;
                    }
                    .database-grid {
                        display: grid;
                        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                        gap: 20px;
                        margin-top: 20px;
                    }
                    .database-card {
                        border: 1px solid #ddd;
                        border-radius: 8px;
                        padding: 20px;
                        background: #fafafa;
                        transition: transform 0.2s, box-shadow 0.2s;
                    }
                    .database-card:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                    }
                    .database-name {
                        font-size: 1.2em;
                        font-weight: bold;
                        color: #333;
                        margin-bottom: 10px;
                    }
                    .database-info {
                        color: #666;
                        font-size: 0.9em;
                        margin: 5px 0;
                    }
                    .database-actions {
                        margin-top: 15px;
                        display: flex;
                        gap: 10px;
                    }
                    .status-active {
                        color: #28a745;
                        font-weight: bold;
                    }
                    .status-inactive {
                        color: #dc3545;
                        font-weight: bold;
                    }
                    .alert {
                        padding: 10px;
                        margin: 10px 0;
                        border-radius: 4px;
                    }
                    .alert-info {
                        background-color: #d1ecf1;
                        color: #0c5460;
                        border: 1px solid #bee5eb;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>Database Management</h1><div>
                            <a href="/app" class="btn btn-secondary">Back to App</a>
                        </div>
                    </div>

                    
                    <div class="alert alert-info">
                        <strong>Mode:</strong> 
                        <span>Multi-Database</span></div>

                    
                    <div id="database-list">
                        <div style="text-align: center; padding: 40px; color: #666;">
                                <h3>No databases found</h3>
                                <p>No databases are available or accessible.</p>
                            </div>
                        </div>
                </div>

                <script>
                    function connectToDatabase(dbName) {
                        window.location.href = &#x27;/app?db=&#x27; + encodeURIComponent(dbName);
                    }
                </script>
            </body>
        </html>
    
<?xml version="1.0" encoding="utf-8"?>
<templates id="template_database_simple" xml:space="preserve">

    <!-- Simple Database Management Page Template -->
    <t t-name="database_simple.html">
        <html>
            <head>
                <title t-esc="title"/>
                <meta charset="utf-8"/>
                <meta name="viewport" content="width=device-width, initial-scale=1"/>
                <style>
                    body {
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        margin: 0;
                        padding: 20px;
                        background-color: #f5f5f5;
                    }
                    .container {
                        max-width: 1200px;
                        margin: 0 auto;
                        background: white;
                        padding: 30px;
                        border-radius: 8px;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    }
                    .header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 30px;
                        padding-bottom: 20px;
                        border-bottom: 2px solid #eee;
                    }
                    .btn {
                        padding: 10px 20px;
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                        text-decoration: none;
                        display: inline-block;
                        font-size: 14px;
                        transition: background-color 0.2s;
                    }
                    .btn-primary {
                        background-color: #007bff;
                        color: white;
                    }
                    .btn-primary:hover {
                        background-color: #0056b3;
                    }
                    .btn-danger {
                        background-color: #dc3545;
                        color: white;
                    }
                    .btn-danger:hover {
                        background-color: #c82333;
                    }
                    .btn-secondary {
                        background-color: #6c757d;
                        color: white;
                    }
                    .btn-secondary:hover {
                        background-color: #545b62;
                    }
                    .database-grid {
                        display: grid;
                        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                        gap: 20px;
                        margin-top: 20px;
                    }
                    .database-card {
                        border: 1px solid #ddd;
                        border-radius: 8px;
                        padding: 20px;
                        background: #fafafa;
                        transition: transform 0.2s, box-shadow 0.2s;
                    }
                    .database-card:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                    }
                    .database-name {
                        font-size: 1.2em;
                        font-weight: bold;
                        color: #333;
                        margin-bottom: 10px;
                    }
                    .database-info {
                        color: #666;
                        font-size: 0.9em;
                        margin: 5px 0;
                    }
                    .database-actions {
                        margin-top: 15px;
                        display: flex;
                        gap: 10px;
                    }
                    .status-active {
                        color: #28a745;
                        font-weight: bold;
                    }
                    .status-inactive {
                        color: #dc3545;
                        font-weight: bold;
                    }
                    .alert {
                        padding: 10px;
                        margin: 10px 0;
                        border-radius: 4px;
                    }
                    .alert-info {
                        background-color: #d1ecf1;
                        color: #0c5460;
                        border: 1px solid #bee5eb;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1 t-esc="title"/>
                        <div>
                            <a href="/app" class="btn btn-secondary">Back to App</a>
                        </div>
                    </div>

                    <!-- Database Mode Info -->
                    <div class="alert alert-info">
                        <strong>Mode:</strong> 
                        <span t-esc="'Multi-Database' if config.is_multi_db_mode else 'Single-Database'"/>
                        <t t-if="config.db_filter">
                            | <strong>Filter:</strong> <span t-esc="config.db_filter"/>
                        </t>
                    </div>

                    <!-- Database List -->
                    <div id="database-list">
                        <t t-if="databases and len(databases) > 0">
                            <div class="database-grid">
                                <t t-foreach="databases" t-as="db">
                                    <div class="database-card">
                                        <div class="database-name" t-esc="db['name']"/>
                                        <div class="database-info">
                                            <div>Size: <span t-esc="db['size']"/></div>
                                            <div>Owner: <span t-esc="db['owner']"/></div>
                                            <div>Encoding: <span t-esc="db['encoding']"/></div>
                                            <div>Status: 
                                                <span t-att-class="'status-active' if db['status'] == 'active' else 'status-inactive'"
                                                      t-esc="db['status']"/>
                                            </div>
                                            <t t-if="db['created']">
                                                <div>Created: <span t-esc="db['created']"/></div>
                                            </t>
                                        </div>
                                        <div class="database-actions">
                                            <button class="btn btn-primary" 
                                                    onclick="connectToDatabase(this.dataset.dbname)"
                                                    t-att-data-dbname="db['name']">
                                                Connect
                                            </button>
                                        </div>
                                    </div>
                                </t>
                            </div>
                        </t>
                        <t t-else="">
                            <div style="text-align: center; padding: 40px; color: #666;">
                                <h3>No databases found</h3>
                                <p>No databases are available or accessible.</p>
                            </div>
                        </t>
                    </div>
                </div>

                <script>
                    function connectToDatabase(dbName) {
                        window.location.href = '/app?db=' + encodeURIComponent(dbName);
                    }
                </script>
            </body>
        </html>
    </t>

</templates>

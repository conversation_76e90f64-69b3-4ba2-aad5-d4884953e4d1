<?xml version="1.0" encoding="utf-8"?>
<templates id="template_database_list_enhanced" xml:space="preserve">

    <!-- Enhanced Database List Page Template -->
    <t t-name="database_list_enhanced.html">
        <html>
            <head>
                <title t-esc="title"/>
                <meta charset="utf-8"/>
                <meta name="viewport" content="width=device-width, initial-scale=1"/>
                <style>
                    * {
                        box-sizing: border-box;
                    }
                    
                    body {
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        margin: 0;
                        padding: 0;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        min-height: 100vh;
                    }
                    
                    .container {
                        max-width: 1200px;
                        margin: 0 auto;
                        padding: 40px 20px;
                    }
                    
                    .header {
                        text-align: center;
                        color: white;
                        margin-bottom: 40px;
                    }
                    
                    .header h1 {
                        font-size: 2.5rem;
                        margin: 0 0 10px 0;
                        font-weight: 300;
                    }
                    
                    .header .subtitle {
                        font-size: 1.1rem;
                        opacity: 0.9;
                        margin-bottom: 20px;
                    }
                    
                    .mode-badge {
                        display: inline-block;
                        background: rgba(255, 255, 255, 0.2);
                        padding: 8px 16px;
                        border-radius: 20px;
                        font-size: 0.9rem;
                        backdrop-filter: blur(10px);
                    }
                    
                    .search-container {
                        margin-bottom: 30px;
                        text-align: center;
                    }
                    
                    .search-box {
                        width: 100%;
                        max-width: 400px;
                        padding: 12px 20px;
                        border: none;
                        border-radius: 25px;
                        font-size: 1rem;
                        background: rgba(255, 255, 255, 0.9);
                        backdrop-filter: blur(10px);
                        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                    }
                    
                    .search-box:focus {
                        outline: none;
                        background: white;
                        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
                    }
                    
                    .database-grid {
                        display: grid;
                        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
                        gap: 25px;
                        margin-bottom: 40px;
                    }
                    
                    .database-card {
                        background: white;
                        border-radius: 15px;
                        padding: 25px;
                        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
                        transition: all 0.3s ease;
                        cursor: pointer;
                        position: relative;
                        overflow: hidden;
                    }
                    
                    .database-card:hover {
                        transform: translateY(-5px);
                        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
                    }
                    
                    .database-card::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        height: 4px;
                        background: linear-gradient(90deg, #667eea, #764ba2);
                    }
                    
                    .database-name {
                        font-size: 1.4rem;
                        font-weight: 600;
                        color: #333;
                        margin-bottom: 15px;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                    }
                    
                    .database-name::before {
                        content: '🗄️';
                        font-size: 1.2rem;
                    }
                    
                    .database-info {
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        gap: 12px;
                        margin-bottom: 20px;
                    }
                    
                    .info-item {
                        display: flex;
                        flex-direction: column;
                        gap: 4px;
                    }
                    
                    .info-label {
                        font-size: 0.8rem;
                        color: #666;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                        font-weight: 500;
                    }
                    
                    .info-value {
                        font-size: 0.95rem;
                        color: #333;
                        font-weight: 500;
                    }
                    
                    .status-active {
                        color: #4caf50;
                    }
                    
                    .status-inactive {
                        color: #f44336;
                    }
                    
                    .connect-btn {
                        width: 100%;
                        padding: 12px;
                        background: linear-gradient(135deg, #667eea, #764ba2);
                        color: white;
                        border: none;
                        border-radius: 8px;
                        font-size: 1rem;
                        font-weight: 500;
                        cursor: pointer;
                        transition: all 0.3s ease;
                    }
                    
                    .connect-btn:hover {
                        background: linear-gradient(135deg, #5a6fd8, #6a4190);
                        transform: translateY(-1px);
                    }
                    
                    .no-databases {
                        text-align: center;
                        color: white;
                        padding: 60px 20px;
                    }
                    
                    .no-databases h3 {
                        font-size: 1.5rem;
                        margin-bottom: 10px;
                        opacity: 0.9;
                    }
                    
                    .no-databases p {
                        opacity: 0.7;
                        font-size: 1.1rem;
                    }
                    
                    .loading {
                        text-align: center;
                        color: white;
                        font-size: 1.2rem;
                        padding: 40px;
                    }
                    
                    .loading::after {
                        content: '';
                        display: inline-block;
                        width: 20px;
                        height: 20px;
                        border: 2px solid rgba(255, 255, 255, 0.3);
                        border-radius: 50%;
                        border-top-color: white;
                        animation: spin 1s ease-in-out infinite;
                        margin-left: 10px;
                    }
                    
                    @keyframes spin {
                        to { transform: rotate(360deg); }
                    }
                    
                    .footer {
                        text-align: center;
                        color: rgba(255, 255, 255, 0.8);
                        font-size: 0.9rem;
                        margin-top: 40px;
                    }
                    
                    @media (max-width: 768px) {
                        .database-grid {
                            grid-template-columns: 1fr;
                        }
                        
                        .header h1 {
                            font-size: 2rem;
                        }
                        
                        .database-info {
                            grid-template-columns: 1fr;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1 t-esc="title"/>
                        <div class="subtitle">Select a database to access your ERP system</div>
                        <div class="mode-badge">
                            <span t-esc="'Multi-Database Mode' if config.is_multi_db_mode else 'Single-Database Mode'"/>
                            <t t-if="config.db_filter">
                                | Filter: <span t-esc="config.db_filter"/>
                            </t>
                        </div>
                    </div>

                    <div class="search-container">
                        <input type="text" class="search-box" placeholder="Search databases..." 
                               id="database-search" onkeyup="filterDatabases()"/>
                    </div>

                    <div id="database-list">
                        <div class="loading">Loading databases...</div>
                    </div>

                    <div class="footer">
                        <p>ERP System v1.0 | Powered by FastAPI &amp; PostgreSQL</p>
                    </div>
                </div>

                <script>
                    let allDatabases = [];

                    // Load database list via API
                    async function loadDatabases() {
                        try {
                            const response = await fetch('/api/databases');
                            const result = await response.json();
                            
                            const container = document.getElementById('database-list');
                            
                            if (result.success &amp;&amp; result.data.length > 0) {
                                allDatabases = result.data;
                                renderDatabases(allDatabases);
                            } else {
                                container.innerHTML = `
                                    &lt;div class="no-databases"&gt;
                                        &lt;h3&gt;No databases found&lt;/h3&gt;
                                        &lt;p&gt;No databases are available or accessible with current filter settings.&lt;/p&gt;
                                    &lt;/div&gt;
                                `;
                            }
                        } catch (error) {
                            console.error('Error loading databases:', error);
                            document.getElementById('database-list').innerHTML = `
                                &lt;div class="no-databases"&gt;
                                    &lt;h3&gt;Error loading databases&lt;/h3&gt;
                                    &lt;p&gt;Unable to connect to the database service. Please try again later.&lt;/p&gt;
                                &lt;/div&gt;
                            `;
                        }
                    }

                    function renderDatabases(databases) {
                        const container = document.getElementById('database-list');
                        
                        if (databases.length === 0) {
                            container.innerHTML = `
                                &lt;div class="no-databases"&gt;
                                    &lt;h3&gt;No matching databases&lt;/h3&gt;
                                    &lt;p&gt;No databases match your search criteria.&lt;/p&gt;
                                &lt;/div&gt;
                            `;
                            return;
                        }

                        container.innerHTML = `
                            &lt;div class="database-grid"&gt;
                                ${databases.map(function(db) {
                                    return `
                                        &lt;div class="database-card" onclick="connectToDatabase('${db.name}')"&gt;
                                            &lt;div class="database-name"&gt;${db.name}&lt;/div&gt;
                                            &lt;div class="database-info"&gt;
                                                &lt;div class="info-item"&gt;
                                                    &lt;div class="info-label"&gt;Size&lt;/div&gt;
                                                    &lt;div class="info-value"&gt;${db.size}&lt;/div&gt;
                                                &lt;/div&gt;
                                                &lt;div class="info-item"&gt;
                                                    &lt;div class="info-label"&gt;Status&lt;/div&gt;
                                                    &lt;div class="info-value status-${db.status}"&gt;${db.status}&lt;/div&gt;
                                                &lt;/div&gt;
                                                &lt;div class="info-item"&gt;
                                                    &lt;div class="info-label"&gt;Owner&lt;/div&gt;
                                                    &lt;div class="info-value"&gt;${db.owner}&lt;/div&gt;
                                                &lt;/div&gt;
                                                &lt;div class="info-item"&gt;
                                                    &lt;div class="info-label"&gt;Encoding&lt;/div&gt;
                                                    &lt;div class="info-value"&gt;${db.encoding}&lt;/div&gt;
                                                &lt;/div&gt;
                                            &lt;/div&gt;
                                            &lt;button class="connect-btn"&gt;Connect to Database&lt;/button&gt;
                                        &lt;/div&gt;
                                    `;
                                }).join('')}
                            &lt;/div&gt;
                        `;
                    }

                    function filterDatabases() {
                        const searchTerm = document.getElementById('database-search').value.toLowerCase();
                        const filteredDatabases = allDatabases.filter(db => 
                            db.name.toLowerCase().includes(searchTerm)
                        );
                        renderDatabases(filteredDatabases);
                    }

                    function connectToDatabase(dbName) {
                        // Add loading state
                        event.target.innerHTML = 'Connecting...';
                        event.target.disabled = true;
                        
                        // Navigate to app with database parameter
                        window.location.href = '/app?db=' + encodeURIComponent(dbName);
                    }

                    // Load databases when page loads
                    document.addEventListener('DOMContentLoaded', loadDatabases);
                </script>
            </body>
        </html>
    </t>

</templates>

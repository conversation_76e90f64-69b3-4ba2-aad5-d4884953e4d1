<?xml version="1.0" encoding="utf-8"?>
<templates id="template_database_list_enhanced" xml:space="preserve">

    <!-- Enhanced Database List Page Template -->
    <t t-name="database_list_enhanced.html">
        <html>
            <head>
                <title t-esc="title"/>
                <meta charset="utf-8"/>
                <meta name="viewport" content="width=device-width, initial-scale=1"/>
                <style>
                    * {
                        box-sizing: border-box;
                    }
                    
                    body {
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        margin: 0;
                        padding: 0;
                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        min-height: 100vh;
                    }
                    
                    .container {
                        max-width: 1200px;
                        margin: 0 auto;
                        padding: 40px 20px;
                    }
                    
                    .header {
                        text-align: center;
                        color: white;
                        margin-bottom: 40px;
                    }
                    
                    .header h1 {
                        font-size: 2.5rem;
                        margin: 0 0 10px 0;
                        font-weight: 300;
                    }
                    
                    .header .subtitle {
                        font-size: 1.1rem;
                        opacity: 0.9;
                        margin-bottom: 20px;
                    }
                    
                    .mode-badge {
                        display: inline-block;
                        background: rgba(255, 255, 255, 0.2);
                        padding: 8px 16px;
                        border-radius: 20px;
                        font-size: 0.9rem;
                        backdrop-filter: blur(10px);
                    }
                    
                    .search-container {
                        margin-bottom: 30px;
                        text-align: center;
                    }
                    
                    .search-box {
                        width: 100%;
                        max-width: 400px;
                        padding: 12px 20px;
                        border: none;
                        border-radius: 25px;
                        font-size: 1rem;
                        background: rgba(255, 255, 255, 0.9);
                        backdrop-filter: blur(10px);
                        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                    }
                    
                    .search-box:focus {
                        outline: none;
                        background: white;
                        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
                    }
                    
                    .database-grid {
                        display: grid;
                        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
                        gap: 25px;
                        margin-bottom: 40px;
                    }
                    
                    .database-card {
                        background: white;
                        border-radius: 15px;
                        padding: 25px;
                        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
                        transition: all 0.3s ease;
                        cursor: pointer;
                        position: relative;
                        overflow: hidden;
                    }
                    
                    .database-card:hover {
                        transform: translateY(-5px);
                        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
                    }
                    
                    .database-card::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        height: 4px;
                        background: linear-gradient(90deg, #667eea, #764ba2);
                    }
                    
                    .database-name {
                        font-size: 1.4rem;
                        font-weight: 600;
                        color: #333;
                        margin-bottom: 15px;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                    }
                    
                    .database-name::before {
                        content: '🗄️';
                        font-size: 1.2rem;
                    }
                    
                    .database-info {
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        gap: 12px;
                        margin-bottom: 20px;
                    }
                    
                    .info-item {
                        display: flex;
                        flex-direction: column;
                        gap: 4px;
                    }
                    
                    .info-label {
                        font-size: 0.8rem;
                        color: #666;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                        font-weight: 500;
                    }
                    
                    .info-value {
                        font-size: 0.95rem;
                        color: #333;
                        font-weight: 500;
                    }
                    
                    .status-active {
                        color: #4caf50;
                    }
                    
                    .status-inactive {
                        color: #f44336;
                    }
                    
                    .connect-btn {
                        width: 100%;
                        padding: 12px;
                        background: linear-gradient(135deg, #667eea, #764ba2);
                        color: white;
                        border: none;
                        border-radius: 8px;
                        font-size: 1rem;
                        font-weight: 500;
                        cursor: pointer;
                        transition: all 0.3s ease;
                    }
                    
                    .connect-btn:hover {
                        background: linear-gradient(135deg, #5a6fd8, #6a4190);
                        transform: translateY(-1px);
                    }
                    
                    .no-databases {
                        text-align: center;
                        color: white;
                        padding: 60px 20px;
                    }
                    
                    .no-databases h3 {
                        font-size: 1.5rem;
                        margin-bottom: 10px;
                        opacity: 0.9;
                    }
                    
                    .no-databases p {
                        opacity: 0.7;
                        font-size: 1.1rem;
                    }
                    
                    .loading {
                        text-align: center;
                        color: white;
                        font-size: 1.2rem;
                        padding: 40px;
                    }
                    
                    .loading::after {
                        content: '';
                        display: inline-block;
                        width: 20px;
                        height: 20px;
                        border: 2px solid rgba(255, 255, 255, 0.3);
                        border-radius: 50%;
                        border-top-color: white;
                        animation: spin 1s ease-in-out infinite;
                        margin-left: 10px;
                    }
                    
                    @keyframes spin {
                        to { transform: rotate(360deg); }
                    }
                    
                    .footer {
                        text-align: center;
                        color: rgba(255, 255, 255, 0.8);
                        font-size: 0.9rem;
                        margin-top: 40px;
                    }
                    
                    @media (max-width: 768px) {
                        .database-grid {
                            grid-template-columns: 1fr;
                        }
                        
                        .header h1 {
                            font-size: 2rem;
                        }
                        
                        .database-info {
                            grid-template-columns: 1fr;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1 t-esc="title"/>
                        <div class="subtitle">Select a database to access your ERP system</div>
                        <div class="mode-badge">
                            <span t-esc="'Multi-Database Mode' if config.is_multi_db_mode else 'Single-Database Mode'"/>
                            <t t-if="config.db_filter">
                                | Filter: <span t-esc="config.db_filter"/>
                            </t>
                        </div>
                    </div>

                    <div class="search-container">
                        <input type="text" class="search-box" placeholder="Search databases..." 
                               id="database-search" onkeyup="filterDatabases()"/>
                    </div>

                    <div id="database-list">
                        <t t-if="databases and len(databases) > 0">
                            <div class="database-grid">
                                <t t-foreach="databases" t-as="db">
                                    <div class="database-card" onclick="connectToDatabase(this.dataset.dbname)" t-att-data-dbname="db['name']">
                                        <div class="database-name" t-esc="db['name']"/>
                                        <div class="database-info">
                                            <div class="info-item">
                                                <div class="info-label">Size</div>
                                                <div class="info-value" t-esc="db['size']"/>
                                            </div>
                                            <div class="info-item">
                                                <div class="info-label">Status</div>
                                                <div t-att-class="'info-value status-' + db['status']" t-esc="db['status']"/>
                                            </div>
                                            <div class="info-item">
                                                <div class="info-label">Owner</div>
                                                <div class="info-value" t-esc="db['owner']"/>
                                            </div>
                                            <div class="info-item">
                                                <div class="info-label">Encoding</div>
                                                <div class="info-value" t-esc="db['encoding']"/>
                                            </div>
                                        </div>
                                        <button class="connect-btn">Connect to Database</button>
                                    </div>
                                </t>
                            </div>
                        </t>
                        <t t-else="">
                            <div class="no-databases">
                                <h3>No databases found</h3>
                                <p>No databases are available or accessible with current filter settings.</p>
                            </div>
                        </t>
                    </div>

                    <div class="footer">
                        <p>ERP System v1.0 | Powered by FastAPI &amp; PostgreSQL</p>
                    </div>
                </div>

                <script>
                    // Get database data from rendered template
                    let allDatabases = [];

                    // Extract database information from rendered cards
                    function initializeDatabases() {
                        const cards = document.querySelectorAll('.database-card');
                        allDatabases = Array.from(cards).map(card => {
                            return {
                                name: card.dataset.dbname,
                                element: card
                            };
                        });
                    }

                    function filterDatabases() {
                        const searchTerm = document.getElementById('database-search').value.toLowerCase();

                        allDatabases.forEach(db => {
                            const shouldShow = db.name.toLowerCase().includes(searchTerm);
                            db.element.style.display = shouldShow ? 'block' : 'none';
                        });

                        // Check if any databases are visible
                        const visibleCards = allDatabases.filter(db =>
                            db.element.style.display !== 'none'
                        );

                        const container = document.getElementById('database-list');
                        const noResultsDiv = container.querySelector('.no-search-results');

                        if (visibleCards.length === 0 &amp;&amp; allDatabases.length > 0) {
                            if (!noResultsDiv) {
                                const noResults = document.createElement('div');
                                noResults.className = 'no-databases no-search-results';
                                noResults.innerHTML = `
                                    <h3>No matching databases</h3>
                                    <p>No databases match your search criteria.</p>
                                `;
                                container.appendChild(noResults);
                            }
                        } else if (noResultsDiv) {
                            noResultsDiv.remove();
                        }
                    }

                    function connectToDatabase(dbName) {
                        // Add loading state
                        const button = event.target;
                        const originalText = button.innerHTML;
                        button.innerHTML = 'Connecting...';
                        button.disabled = true;

                        // Navigate to app with database parameter
                        window.location.href = '/app?db=' + encodeURIComponent(dbName);
                    }

                    // Initialize when page loads
                    document.addEventListener('DOMContentLoaded', initializeDatabases);
                </script>
            </body>
        </html>
    </t>

</templates>

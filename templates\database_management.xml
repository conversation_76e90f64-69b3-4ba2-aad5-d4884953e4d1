<?xml version="1.0" encoding="utf-8"?>
<templates id="template_database_management" xml:space="preserve">

    <!-- Database Management Page Template -->
    <t t-name="database_management.html">
        <html>
            <head>
                <title t-esc="title"/>
                <meta charset="utf-8"/>
                <meta name="viewport" content="width=device-width, initial-scale=1"/>
                <style>
                    body {
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        margin: 0;
                        padding: 20px;
                        background-color: #f5f5f5;
                    }
                    .container {
                        max-width: 1200px;
                        margin: 0 auto;
                        background: white;
                        padding: 30px;
                        border-radius: 8px;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    }
                    .header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 30px;
                        padding-bottom: 20px;
                        border-bottom: 2px solid #eee;
                    }
                    .btn {
                        padding: 10px 20px;
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                        text-decoration: none;
                        display: inline-block;
                        font-size: 14px;
                        transition: background-color 0.2s;
                    }
                    .btn-primary {
                        background-color: #007bff;
                        color: white;
                    }
                    .btn-primary:hover {
                        background-color: #0056b3;
                    }
                    .btn-danger {
                        background-color: #dc3545;
                        color: white;
                    }
                    .btn-danger:hover {
                        background-color: #c82333;
                    }
                    .btn-secondary {
                        background-color: #6c757d;
                        color: white;
                    }
                    .btn-secondary:hover {
                        background-color: #545b62;
                    }
                    .database-grid {
                        display: grid;
                        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                        gap: 20px;
                        margin-top: 20px;
                    }
                    .database-card {
                        border: 1px solid #ddd;
                        border-radius: 8px;
                        padding: 20px;
                        background: #fafafa;
                        transition: transform 0.2s, box-shadow 0.2s;
                    }
                    .database-card:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                    }
                    .database-name {
                        font-size: 1.2em;
                        font-weight: bold;
                        color: #333;
                        margin-bottom: 10px;
                    }
                    .database-info {
                        color: #666;
                        font-size: 0.9em;
                        margin: 5px 0;
                    }
                    .database-actions {
                        margin-top: 15px;
                        display: flex;
                        gap: 10px;
                    }
                    .status-active {
                        color: #28a745;
                        font-weight: bold;
                    }
                    .status-inactive {
                        color: #dc3545;
                        font-weight: bold;
                    }
                    .modal {
                        display: none;
                        position: fixed;
                        z-index: 1000;
                        left: 0;
                        top: 0;
                        width: 100%;
                        height: 100%;
                        background-color: rgba(0,0,0,0.5);
                    }
                    .modal-content {
                        background-color: white;
                        margin: 15% auto;
                        padding: 20px;
                        border-radius: 8px;
                        width: 400px;
                        max-width: 90%;
                    }
                    .form-group {
                        margin-bottom: 15px;
                    }
                    .form-group label {
                        display: block;
                        margin-bottom: 5px;
                        font-weight: bold;
                    }
                    .form-group input, .form-group select {
                        width: 100%;
                        padding: 8px;
                        border: 1px solid #ddd;
                        border-radius: 4px;
                        box-sizing: border-box;
                    }
                    .alert {
                        padding: 10px;
                        margin: 10px 0;
                        border-radius: 4px;
                    }
                    .alert-success {
                        background-color: #d4edda;
                        color: #155724;
                        border: 1px solid #c3e6cb;
                    }
                    .alert-error {
                        background-color: #f8d7da;
                        color: #721c24;
                        border: 1px solid #f5c6cb;
                    }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1 t-esc="title"/>
                        <div>
                            <button class="btn btn-primary" onclick="showCreateModal()">
                                + Create Database
                            </button>
                            <a href="/app" class="btn btn-secondary">Back to App</a>
                        </div>
                    </div>

                    <!-- Database Mode Info -->
                    <div class="alert alert-info" style="background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb;">
                        <strong>Mode:</strong> 
                        <span t-esc="'Multi-Database' if config.is_multi_db_mode else 'Single-Database'"/>
                        <t t-if="config.db_filter">
                            | <strong>Filter:</strong> <span t-esc="config.db_filter"/>
                        </t>
                    </div>

                    <!-- Database List -->
                    <div id="database-list">
                        <t t-if="databases and len(databases) > 0">
                            <div class="database-grid">
                                <t t-foreach="databases" t-as="db">
                                    <div class="database-card">
                                        <div class="database-name" t-esc="db.name"/>
                                        <div class="database-info">
                                            <div>Size: <span t-esc="db.size"/></div>
                                            <div>Owner: <span t-esc="db.owner"/></div>
                                            <div>Encoding: <span t-esc="db.encoding"/></div>
                                            <div>Status: 
                                                <span t-att-class="'status-active' if db.status == 'active' else 'status-inactive'"
                                                      t-esc="db.status"/>
                                            </div>
                                            <t t-if="db.created">
                                                <div>Created: <span t-esc="db.created"/></div>
                                            </t>
                                        </div>
                                        <div class="database-actions">
                                            <button class="btn btn-primary" 
                                                    t-att-onclick="'connectToDatabase(\'' + db.name + '\')'">
                                                Connect
                                            </button>
                                            <t t-if="config.is_multi_db_mode and db.name not in ['postgres', 'template0', 'template1']">
                                                <button class="btn btn-danger" 
                                                        t-att-onclick="'confirmDeleteDatabase(\'' + db.name + '\')'">
                                                    Delete
                                                </button>
                                            </t>
                                        </div>
                                    </div>
                                </t>
                            </div>
                        </t>
                        <t t-else="">
                            <div style="text-align: center; padding: 40px; color: #666;">
                                <h3>No databases found</h3>
                                <p>Create your first database to get started.</p>
                            </div>
                        </t>
                    </div>

                    <!-- Loading indicator -->
                    <div id="loading" style="display: none; text-align: center; padding: 40px;">
                        <div>Loading databases...</div>
                    </div>

                    <!-- Error display -->
                    <div id="error-display" style="display: none;" class="alert alert-error">
                        <span id="error-message"></span>
                    </div>
                </div>

                <!-- Create Database Modal -->
                <div id="createModal" class="modal">
                    <div class="modal-content">
                        <h3>Create New Database</h3>
                        <form id="createForm">
                            <div class="form-group">
                                <label for="dbName">Database Name:</label>
                                <input type="text" id="dbName" name="name" required
                                       title="Must start with a letter and contain only letters, numbers, and underscores"/>
                            </div>
                            <div class="form-group">
                                <label for="language">Language:</label>
                                <select id="language" name="language">
                                    <option value="en_US">English (US)</option>
                                    <option value="en_GB">English (UK)</option>
                                    <option value="fr_FR">French</option>
                                    <option value="de_DE">German</option>
                                    <option value="es_ES">Spanish</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="demo" name="demo"/> 
                                    Install demo data
                                </label>
                            </div>
                            <div style="text-align: right; margin-top: 20px;">
                                <button type="button" class="btn btn-secondary" onclick="hideCreateModal()">
                                    Cancel
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    Create Database
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Delete Confirmation Modal -->
                <div id="deleteModal" class="modal">
                    <div class="modal-content">
                        <h3>Confirm Database Deletion</h3>
                        <p>Are you sure you want to delete the database <strong id="deleteDbName"></strong>?</p>
                        <p style="color: #dc3545; font-weight: bold;">This action cannot be undone!</p>
                        <div style="text-align: right; margin-top: 20px;">
                            <button type="button" class="btn btn-secondary" onclick="hideDeleteModal()">
                                Cancel
                            </button>
                            <button type="button" class="btn btn-danger" onclick="deleteDatabase()">
                                Delete Database
                            </button>
                        </div>
                    </div>
                </div>

                <script>
                    let currentDeleteDb = null;

                    function showCreateModal() {
                        document.getElementById('createModal').style.display = 'block';
                    }

                    function hideCreateModal() {
                        document.getElementById('createModal').style.display = 'none';
                        document.getElementById('createForm').reset();
                    }

                    function confirmDeleteDatabase(dbName) {
                        currentDeleteDb = dbName;
                        document.getElementById('deleteDbName').textContent = dbName;
                        document.getElementById('deleteModal').style.display = 'block';
                    }

                    function hideDeleteModal() {
                        document.getElementById('deleteModal').style.display = 'none';
                        currentDeleteDb = null;
                    }

                    function connectToDatabase(dbName) {
                        window.location.href = '/app?db=' + encodeURIComponent(dbName);
                    }

                    async function deleteDatabase() {
                        if (!currentDeleteDb) return;

                        try {
                            const response = await fetch('/api/databases/' + encodeURIComponent(currentDeleteDb), {
                                method: 'DELETE'
                            });
                            
                            const result = await response.json();
                            
                            if (result.success) {
                                hideDeleteModal();
                                location.reload(); // Refresh the page to update the list
                            } else {
                                showError('Failed to delete database: ' + result.message);
                            }
                        } catch (error) {
                            showError('Error deleting database: ' + error.message);
                        }
                    }

                    // Handle create form submission
                    document.getElementById('createForm').addEventListener('submit', async function(e) {
                        e.preventDefault();
                        
                        const formData = new FormData(e.target);
                        const data = {
                            name: formData.get('name'),
                            language: formData.get('language'),
                            demo: formData.has('demo')
                        };

                        try {
                            const response = await fetch('/api/databases', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify(data)
                            });
                            
                            const result = await response.json();
                            
                            if (result.success) {
                                hideCreateModal();
                                location.reload(); // Refresh the page to update the list
                            } else {
                                showError('Failed to create database: ' + result.message);
                            }
                        } catch (error) {
                            showError('Error creating database: ' + error.message);
                        }
                    });

                    function showError(message) {
                        document.getElementById('error-message').textContent = message;
                        document.getElementById('error-display').style.display = 'block';
                        setTimeout(() => {
                            document.getElementById('error-display').style.display = 'none';
                        }, 5000);
                    }

                    // Close modals when clicking outside
                    window.onclick = function(event) {
                        const createModal = document.getElementById('createModal');
                        const deleteModal = document.getElementById('deleteModal');
                        
                        if (event.target === createModal) {
                            hideCreateModal();
                        }
                        if (event.target === deleteModal) {
                            hideDeleteModal();
                        }
                    }
                </script>
            </body>
        </html>
    </t>

</templates>
